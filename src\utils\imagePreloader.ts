import { PREMIUM_IMAGES, batchPreloadImages, preloadCriticalImages } from './imageUtils';

class ImagePreloader {
  private static instance: ImagePreloader;
  private preloadedImages = new Set<string>();
  private loadingPromises = new Map<string, Promise<void>>();

  static getInstance(): ImagePreloader {
    if (!ImagePreloader.instance) {
      ImagePreloader.instance = new ImagePreloader();
    }
    return ImagePreloader.instance;
  }

  // Preload critical images immediately
  async preloadCritical(): Promise<void> {
    const criticalImages = [
      PREMIUM_IMAGES.hero.manufacturing,
      PREMIUM_IMAGES.hero.precision,
      PREMIUM_IMAGES.hero.facility,
    ];

    preloadCriticalImages(criticalImages);

    try {
      const result = await batchPreloadImages(criticalImages);
      console.log(`Preloaded ${result.loaded}/${result.total} critical images`);

      if (result.errors.length > 0) {
        console.warn('Failed to preload some critical images:', result.errors);
      }
    } catch (error) {
      console.error('Critical image preloading failed:', error);
    }
  }

  // Preload images by priority
  async preloadByPriority(priority: 'high' | 'medium' | 'low'): Promise<void> {
    const imageGroups = {
      high: [...Object.values(PREMIUM_IMAGES.hero), ...Object.values(PREMIUM_IMAGES.manufacturing)],
      medium: [
        ...Object.values(PREMIUM_IMAGES.products),
        ...Object.values(PREMIUM_IMAGES.portfolio),
      ],
      low: [...Object.values(PREMIUM_IMAGES.innovation), ...Object.values(PREMIUM_IMAGES.about)],
    };

    const images = imageGroups[priority];
    const filteredImages = images.filter(img => !this.preloadedImages.has(img));

    if (filteredImages.length === 0) return;

    try {
      const result = await batchPreloadImages(filteredImages);
      filteredImages.forEach(img => this.preloadedImages.add(img));

      console.log(`Preloaded ${result.loaded}/${result.total} ${priority} priority images`);
    } catch (error) {
      console.error(`${priority} priority image preloading failed:`, error);
    }
  }

  // Preload images for a specific page
  async preloadPageImages(page: string): Promise<void> {
    const pageImageMap: Record<string, string[]> = {
      home: [
        ...Object.values(PREMIUM_IMAGES.hero),
        ...Object.values(PREMIUM_IMAGES.manufacturing).slice(0, 2),
      ],
      about: [...Object.values(PREMIUM_IMAGES.about), PREMIUM_IMAGES.hero.facility],
      services: [PREMIUM_IMAGES.services.overview, ...Object.values(PREMIUM_IMAGES.manufacturing)],
      products: [...Object.values(PREMIUM_IMAGES.products)],
      portfolio: [...Object.values(PREMIUM_IMAGES.portfolio)],
      quality: [...Object.values(PREMIUM_IMAGES.quality), PREMIUM_IMAGES.manufacturing.quality],
      sustainability: [...Object.values(PREMIUM_IMAGES.sustainability)],
      news: [PREMIUM_IMAGES.news.overview, ...PREMIUM_IMAGES.news.articles.slice(0, 3)],
      careers: [...Object.values(PREMIUM_IMAGES.careers)],
    };

    const images = pageImageMap[page] || [];
    const filteredImages = images.filter(img => !this.preloadedImages.has(img));

    if (filteredImages.length === 0) return;

    try {
      const result = await batchPreloadImages(filteredImages);
      filteredImages.forEach(img => this.preloadedImages.add(img));

      console.log(`Preloaded ${result.loaded}/${result.total} images for ${page} page`);
    } catch (error) {
      console.error(`Page image preloading failed for ${page}:`, error);
    }
  }

  // Intelligent preloading based on user behavior
  async intelligentPreload(): Promise<void> {
    // Start with critical images
    await this.preloadCritical();

    // Preload high priority images after a short delay
    setTimeout(() => {
      this.preloadByPriority('high');
    }, 1000);

    // Preload medium priority images when idle
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        this.preloadByPriority('medium');
      });
    } else {
      setTimeout(() => {
        this.preloadByPriority('medium');
      }, 3000);
    }

    // Preload low priority images when network is fast
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection && connection.effectiveType === '4g') {
        setTimeout(() => {
          this.preloadByPriority('low');
        }, 5000);
      }
    }
  }

  // Preload images on hover (for navigation)
  preloadOnHover(page: string): void {
    if (!this.loadingPromises.has(page)) {
      const promise = this.preloadPageImages(page);
      this.loadingPromises.set(page, promise);
    }
  }

  // Get preload status
  getPreloadStatus(): {
    totalPreloaded: number;
    criticalLoaded: boolean;
    highPriorityLoaded: boolean;
  } {
    const criticalImages = [
      PREMIUM_IMAGES.hero.manufacturing,
      PREMIUM_IMAGES.hero.precision,
      PREMIUM_IMAGES.hero.facility,
    ];

    const highPriorityImages = [
      ...Object.values(PREMIUM_IMAGES.hero),
      ...Object.values(PREMIUM_IMAGES.manufacturing),
    ];

    const criticalLoaded = criticalImages.every(img => this.preloadedImages.has(img));
    const highPriorityLoaded = highPriorityImages.every(img => this.preloadedImages.has(img));

    return {
      totalPreloaded: this.preloadedImages.size,
      criticalLoaded,
      highPriorityLoaded,
    };
  }

  // Clear preloaded images cache
  clearCache(): void {
    this.preloadedImages.clear();
    this.loadingPromises.clear();
  }
}

// Export singleton instance
export const imagePreloader = ImagePreloader.getInstance();

// Initialize preloading when module loads
export const initializeImagePreloading = () => {
  // Start intelligent preloading
  imagePreloader.intelligentPreload();

  // Add hover preloading to navigation links
  document.addEventListener('DOMContentLoaded', () => {
    const navLinks = document.querySelectorAll('[data-page]');
    navLinks.forEach(link => {
      link.addEventListener('mouseenter', () => {
        const page = link.getAttribute('data-page');
        if (page) {
          imagePreloader.preloadOnHover(page);
        }
      });
    });
  });
};

export default ImagePreloader;
