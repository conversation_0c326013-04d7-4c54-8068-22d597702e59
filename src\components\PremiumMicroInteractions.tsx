import React, { useEffect, useState } from 'react';

interface MicroInteractionProps {
  children: React.ReactNode;
  type?: 'hover' | 'click' | 'scroll';
  delay?: number;
  className?: string;
}

const PremiumMicroInteractions: React.FC<MicroInteractionProps> = ({
  children,
  type = 'hover',
  delay = 0,
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isInteracted, setIsInteracted] = useState(false);

  useEffect(() => {
    if (type === 'scroll') {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setTimeout(() => setIsVisible(true), delay);
          }
        },
        { threshold: 0.1 }
      );

      const element = document.querySelector(`.${className}`);
      if (element) observer.observe(element);

      return () => observer.disconnect();
    }
  }, [type, delay, className]);

  const handleInteraction = () => {
    if (type === 'click') {
      setIsInteracted(!isInteracted);
    }
  };

  const getInteractionClasses = () => {
    const baseClasses = 'luxury-button-micro luxury-ripple';

    switch (type) {
      case 'hover':
        return `${baseClasses} luxury-card-hover luxury-glow-trail`;
      case 'click':
        return `${baseClasses} ${isInteracted ? 'luxury-elastic' : ''}`;
      case 'scroll':
        return `${baseClasses} ${isVisible ? 'luxury-animate-fade-up luxury-text-reveal' : 'opacity-0'}`;
      default:
        return baseClasses;
    }
  };

  return (
    <div
      className={`${getInteractionClasses()} ${className} transition-all duration-300`}
      onClick={handleInteraction}
      onMouseEnter={() => type === 'hover' && setIsInteracted(true)}
      onMouseLeave={() => type === 'hover' && setIsInteracted(false)}
      style={{
        animationDelay: `${delay}ms`,
      }}>
      {children}
    </div>
  );
};

export default PremiumMicroInteractions;
