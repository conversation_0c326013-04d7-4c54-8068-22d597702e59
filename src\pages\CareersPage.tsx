import { ArrowRight, Award, Briefcase, Clock, MapPin, Users } from 'lucide-react';
import React from 'react';

const CareersPage: React.FC = () => {
  const benefits = [
    {
      icon: Award,
      title: 'Competitive Compensation',
      description: 'Industry-leading salaries and performance-based bonuses',
    },
    {
      icon: Users,
      title: 'Professional Development',
      description: 'Continuous learning opportunities and career advancement',
    },
    {
      icon: Clock,
      title: 'Work-Life Balance',
      description: 'Flexible schedules and comprehensive time-off policies',
    },
    {
      icon: Briefcase,
      title: 'Innovation Culture',
      description: 'Work on cutting-edge projects with industry-leading technology',
    },
  ];

  const openPositions = [
    {
      title: 'Senior Manufacturing Engineer',
      department: 'Engineering',
      location: 'New York, NY',
      type: 'Full-time',
      experience: '5+ years',
      description:
        'Lead manufacturing process optimization and new product development initiatives.',
      requirements: [
        "Bachelor's in Mechanical/Manufacturing Engineering",
        'Experience with aluminum processing',
        'Lean manufacturing knowledge',
      ],
    },
    {
      title: 'Quality Assurance Specialist',
      department: 'Quality',
      location: 'New York, NY',
      type: 'Full-time',
      experience: '3+ years',
      description: 'Ensure product quality through comprehensive testing and process validation.',
      requirements: [
        'Quality management experience',
        'ISO 9001 knowledge',
        'Statistical analysis skills',
      ],
    },
    {
      title: 'R&D Materials Scientist',
      department: 'Research & Development',
      location: 'New York, NY',
      type: 'Full-time',
      experience: '4+ years',
      description: 'Develop next-generation aluminum alloys and surface treatment technologies.',
      requirements: [
        'PhD in Materials Science',
        'Aluminum metallurgy expertise',
        'Research publication record',
      ],
    },
    {
      title: 'Production Supervisor',
      department: 'Operations',
      location: 'New York, NY',
      type: 'Full-time',
      experience: '3+ years',
      description: 'Oversee daily production operations and team management.',
      requirements: [
        'Manufacturing supervision experience',
        'Team leadership skills',
        'Safety management knowledge',
      ],
    },
  ];

  const values = [
    'Innovation Excellence',
    'Quality Commitment',
    'Environmental Responsibility',
    'Team Collaboration',
    'Continuous Improvement',
    'Customer Focus',
  ];

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link sr-only focus:not-sr-only">
        Skip to main content
      </a>

      {/* Hero Section */}
      <section
        id="main-content"
        className="relative h-screen flex items-center justify-center overflow-hidden pt-20">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop"
            alt="WINASTRA team"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-b from-black/70 via-black/50 to-black/80"></div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-tight leading-none">
            <span className="metallic-gradient">JOIN OUR</span>
            <br />
            <span className="text-white font-light">TEAM</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Build your career with industry leaders in aluminum manufacturing. Shape the future of
            precision engineering and innovation.
          </p>
        </div>
      </section>

      {/* Why Work With Us */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Why WINASTRA?</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Join a team that values innovation, excellence, and personal growth. We offer more
              than just a job – we offer a career that makes a difference.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="text-center group animate-on-scroll hover-lift"
                style={{ animationDelay: `${index * 0.2}s` }}>
                <div className="glass-effect rounded-2xl p-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 mb-6 group-hover:scale-110 transition-transform duration-300">
                    <benefit.icon className="w-8 h-8 text-black" />
                  </div>

                  <h3 className="text-xl font-semibold text-white mb-4">{benefit.title}</h3>

                  <p className="text-gray-400 leading-relaxed">{benefit.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Company Culture */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-on-scroll">
              <h2 className="text-4xl md:text-5xl font-thin text-white mb-8">Our Culture</h2>
              <p className="text-lg text-gray-300 mb-8 leading-relaxed">
                At WINASTRA, we foster an environment where innovation thrives, collaboration is
                valued, and every team member has the opportunity to make a meaningful impact on our
                industry.
              </p>

              <div className="grid grid-cols-2 gap-4 mb-8">
                {values.map((value, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full"></div>
                    <span className="text-gray-300 text-sm">{value}</span>
                  </div>
                ))}
              </div>

              <div className="glass-effect rounded-2xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">Employee Testimonial</h4>
                <p className="text-gray-400 italic leading-relaxed mb-4">
                  "Working at WINASTRA has been an incredible journey. The company's commitment to
                  innovation and employee development has allowed me to grow both professionally and
                  personally."
                </p>
                <div className="text-sm text-gray-500">- Sarah Chen, Senior Engineer</div>
              </div>
            </div>

            <div className="animate-on-scroll">
              <img
                src="https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Team collaboration"
                className="w-full h-96 object-cover rounded-2xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-20 bg-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Open Positions</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Explore current opportunities to join our team and contribute to the future of
              aluminum manufacturing.
            </p>
          </div>

          <div className="space-y-6">
            {openPositions.map((position, index) => (
              <div
                key={index}
                className="glass-effect rounded-2xl p-6 hover-lift animate-on-scroll"
                style={{ animationDelay: `${index * 0.1}s` }}>
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-4 mb-4">
                      <h3 className="text-xl font-semibold text-white">{position.title}</h3>
                      <span className="px-3 py-1 bg-gradient-to-r from-gray-400 to-yellow-500 text-black text-xs rounded-full font-medium">
                        {position.department}
                      </span>
                    </div>

                    <div className="flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-400">
                      <span className="flex items-center space-x-1">
                        <MapPin size={14} />
                        <span>{position.location}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Clock size={14} />
                        <span>{position.type}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Briefcase size={14} />
                        <span>{position.experience}</span>
                      </span>
                    </div>

                    <p className="text-gray-300 mb-4 leading-relaxed">{position.description}</p>

                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-white mb-2">Key Requirements:</h4>
                      <ul className="space-y-1">
                        {position.requirements.map((req, reqIndex) => (
                          <li key={reqIndex} className="text-sm text-gray-400 flex items-center">
                            <div className="w-1.5 h-1.5 bg-gradient-to-r from-gray-400 to-yellow-500 rounded-full mr-3"></div>
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="lg:ml-8">
                    <button className="metallic-button px-6 py-3 rounded-lg font-medium flex items-center space-x-2 hover:scale-105 transition-transform duration-300">
                      <span>Apply Now</span>
                      <ArrowRight size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Application Process */}
      <section className="py-20 bg-gradient-to-b from-black to-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-on-scroll">
            <h2 className="text-4xl md:text-5xl font-thin text-white mb-6">Application Process</h2>
            <p className="text-lg text-gray-400 max-w-3xl mx-auto">
              Our streamlined hiring process is designed to find the best talent while providing a
              positive candidate experience.
            </p>
          </div>

          <div className="glass-effect rounded-3xl p-8 md:p-12 animate-on-scroll">
            <div className="grid md:grid-cols-4 gap-8">
              {[
                {
                  step: '01',
                  title: 'Application',
                  description: 'Submit your resume and cover letter',
                },
                {
                  step: '02',
                  title: 'Screening',
                  description: 'Initial review and phone screening',
                },
                {
                  step: '03',
                  title: 'Interview',
                  description: 'Technical and cultural fit assessment',
                },
                { step: '04', title: 'Offer', description: 'Reference check and job offer' },
              ].map((process, index) => (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 rounded-full bg-gradient-to-r from-gray-400 to-yellow-500 flex items-center justify-center mx-auto mb-4">
                    <span className="text-black font-bold mono-font">{process.step}</span>
                  </div>
                  <h4 className="text-lg font-semibold text-white mb-2">{process.title}</h4>
                  <p className="text-gray-400 text-sm">{process.description}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default CareersPage;
