import { <PERSON><PERSON><PERSON>, Eye, Maximize2, Star } from 'lucide-react';
import React, { useState } from 'react';

const PremiumProductShowcase: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('Featured');
  const [hoveredProduct, setHoveredProduct] = useState<number | null>(null);

  const categories = ['Featured', 'Architectural', 'Aerospace', 'Marine', 'Industrial'];

  const products = [
    {
      id: 1,
      title: 'Architectural Excellence Series',
      category: 'Featured',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
      description: 'Premium aluminum extrusions for luxury architectural applications',
      specs: ['6061-T6 Alloy', 'Custom Profiles', 'Anodized Finish'],
      rating: 5,
      featured: true,
      price: 'Premium',
      applications: ['Curtain Walls', 'Structural Glazing', 'Facade Systems'],
    },
    {
      id: 2,
      title: 'Aerospace Precision Components',
      category: 'Aerospace',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
      description: 'Ultra-precision components meeting aerospace industry standards',
      specs: ['7075-T6 Alloy', 'AS9100 Certified', 'Critical Tolerances'],
      rating: 5,
      featured: true,
      price: 'Ultra Premium',
      applications: ['Aircraft Structures', 'Engine Components', 'Landing Gear'],
    },
    {
      id: 3,
      title: 'Marine Grade Solutions',
      category: 'Marine',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
      description: 'Corrosion-resistant aluminum for marine environments',
      specs: ['5083-H116 Alloy', 'Salt Water Resistant', 'Marine Certified'],
      rating: 5,
      featured: false,
      price: 'Premium',
      applications: ['Yacht Construction', 'Marine Hardware', 'Offshore Platforms'],
    },
    {
      id: 4,
      title: 'Industrial Strength Profiles',
      category: 'Industrial',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
      description: 'Heavy-duty aluminum profiles for industrial applications',
      specs: ['6082-T6 Alloy', 'High Load Capacity', 'Precision Machined'],
      rating: 4,
      featured: false,
      price: 'Standard',
      applications: ['Automation Systems', 'Conveyor Frames', 'Machine Guards'],
    },
    {
      id: 5,
      title: 'Architectural Decorative Panels',
      category: 'Architectural',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
      description: 'Aesthetic aluminum panels with premium surface treatments',
      specs: ['Multiple Alloys', 'Custom Finishes', 'Artistic Patterns'],
      rating: 5,
      featured: false,
      price: 'Luxury',
      applications: ['Interior Design', 'Facade Cladding', 'Decorative Elements'],
    },
    {
      id: 6,
      title: 'Custom Engineering Solutions',
      category: 'Featured',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
      description: 'Bespoke aluminum solutions for unique requirements',
      specs: ['Multiple Alloys', 'Complex Geometries', 'Tight Tolerances'],
      rating: 5,
      featured: true,
      price: 'Bespoke',
      applications: ['Custom Projects', 'Prototyping', 'Specialized Components'],
    },
  ];

  const filteredProducts =
    activeCategory === 'Featured'
      ? products.filter(product => product.featured)
      : products.filter(product => product.category === activeCategory);

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={14}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}
      />
    ));
  };

  return (
    <section id="products" className="py-24 bg-gradient-to-b from-black via-gray-900 to-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Premium Section Header */}
        <div className="text-center mb-20 luxury-animate-fade-up">
          <div className="flex items-center justify-center mb-6">
            <div className="w-12 h-0.5 bg-gradient-to-r from-transparent to-yellow-400"></div>
            <span className="luxury-caption text-gray-400 mx-6">Product Portfolio</span>
            <div className="w-12 h-0.5 bg-gradient-to-l from-transparent to-yellow-400"></div>
          </div>

          <h2 className="luxury-heading-display text-5xl md:text-7xl mb-8">
            <span className="luxury-gradient-platinum">Premium</span>
            <span className="luxury-gradient-gold block font-light">Collections</span>
          </h2>

          <p className="luxury-body-large text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Discover our meticulously crafted aluminum solutions, each engineered to exceed the
            highest standards of quality, precision, and aesthetic excellence.
          </p>
        </div>

        {/* Premium Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-16 luxury-animate-fade-up">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => handleCategoryChange(category)}
              className={`px-8 py-4 rounded-full text-sm font-medium transition-all duration-500 relative overflow-hidden group ${activeCategory === category
                  ? 'luxury-button-primary'
                  : 'luxury-glass-primary text-gray-300 hover:text-white luxury-hover-glow'
                }`}
              aria-pressed={activeCategory === category}>
              <span className="relative z-10">{category}</span>
              {activeCategory !== category && (
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              )}
            </button>
          ))}
        </div>

        {/* Premium Products Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProducts.map((product, index) => (
            <div
              key={product.id}
              className="group luxury-hover-lift luxury-animate-fade-up"
              style={{ animationDelay: `${index * 0.1}s` }}
              onMouseEnter={() => setHoveredProduct(product.id)}
              onMouseLeave={() => setHoveredProduct(null)}>
              <div className="luxury-glass-secondary rounded-3xl overflow-hidden h-full relative">
                {/* Premium Badge */}
                {product.featured && (
                  <div className="absolute top-4 left-4 z-20">
                    <span className="luxury-glass-accent px-3 py-1 rounded-full text-xs font-medium text-yellow-400 luxury-shimmer">
                      Featured
                    </span>
                  </div>
                )}

                {/* Product Image with Premium Effects */}
                <div className="relative overflow-hidden h-64">
                  <img
                    src={product.image}
                    alt={product.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />

                  {/* Premium Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div className="absolute bottom-4 right-4 flex space-x-2">
                      <button className="w-10 h-10 rounded-full luxury-glass-accent flex items-center justify-center luxury-hover-glow">
                        <Eye size={16} className="text-yellow-400" />
                      </button>
                      <button className="w-10 h-10 rounded-full luxury-glass-accent flex items-center justify-center luxury-hover-glow">
                        <Maximize2 size={16} className="text-yellow-400" />
                      </button>
                    </div>
                  </div>

                  {/* Price Badge */}
                  <div className="absolute top-4 right-4">
                    <span className="luxury-glass-primary px-3 py-1 rounded-full text-xs font-medium text-gray-300">
                      {product.price}
                    </span>
                  </div>
                </div>

                {/* Premium Content */}
                <div className="p-8">
                  {/* Rating */}
                  <div className="flex items-center space-x-1 mb-3">
                    {renderStars(product.rating)}
                    <span className="text-xs text-gray-500 ml-2">({product.rating}.0)</span>
                  </div>

                  {/* Title and Category */}
                  <div className="mb-4">
                    <span className="luxury-caption text-yellow-400 mb-2 block">
                      {product.category}
                    </span>
                    <h3 className="luxury-heading-secondary text-xl text-white mb-3 group-hover:luxury-gradient-gold transition-all duration-300">
                      {product.title}
                    </h3>
                  </div>

                  {/* Description */}
                  <p className="luxury-body-medium text-gray-400 mb-6 leading-relaxed">
                    {product.description}
                  </p>

                  {/* Specifications */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-white mb-3">Specifications</h4>
                    <div className="flex flex-wrap gap-2">
                      {product.specs.map((spec, specIndex) => (
                        <span
                          key={specIndex}
                          className="px-3 py-1 luxury-glass-primary rounded-lg text-xs text-gray-300">
                          {spec}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Applications */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-white mb-3">Applications</h4>
                    <div className="space-y-1">
                      {product.applications.slice(0, 2).map((app, appIndex) => (
                        <div key={appIndex} className="flex items-center text-xs text-gray-400">
                          <div className="w-1 h-1 bg-yellow-400 rounded-full mr-2"></div>
                          {app}
                        </div>
                      ))}
                      {product.applications.length > 2 && (
                        <div className="text-xs text-gray-500">
                          +{product.applications.length - 2} more
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Premium CTA */}
                  <div className="flex items-center justify-between">
                    <button className="luxury-button-secondary text-sm px-6 py-2 group">
                      <span className="flex items-center space-x-2">
                        <span>Learn More</span>
                        <ArrowRight
                          size={14}
                          className="group-hover:translate-x-1 transition-transform duration-300"
                        />
                      </span>
                    </button>

                    <button className="text-yellow-400 hover:text-yellow-300 transition-colors duration-300">
                      <span className="text-xs">Request Quote</span>
                    </button>
                  </div>
                </div>

                {/* Premium Hover Effect */}
                <div
                  className={`absolute inset-0 rounded-3xl transition-opacity duration-500 pointer-events-none ${hoveredProduct === product.id ? 'opacity-100' : 'opacity-0'
                    }`}>
                  <div className="absolute inset-0 rounded-3xl bg-gradient-to-r from-yellow-400/5 to-transparent"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Premium CTA Section */}
        <div className="text-center mt-16 luxury-animate-fade-up">
          <div className="luxury-glass-accent rounded-3xl p-12 max-w-4xl mx-auto">
            <h3 className="luxury-heading-secondary text-3xl text-white mb-6">
              Need Something Unique?
            </h3>
            <p className="luxury-body-large text-gray-300 mb-8 max-w-2xl mx-auto">
              Our engineering team specializes in creating bespoke aluminum solutions tailored to
              your exact specifications and requirements.
            </p>
            <button className="luxury-button-primary">Start Custom Project</button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PremiumProductShowcase;
