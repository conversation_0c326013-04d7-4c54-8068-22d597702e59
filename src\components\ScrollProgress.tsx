import React from 'react';
import { useScrollProgress } from '../hooks/useScroll';

interface ScrollProgressProps {
  variant?: 'standard' | 'premium';
  className?: string;
}

const ScrollProgress: React.FC<ScrollProgressProps> = ({
  variant = 'standard',
  className = '',
}) => {
  const scrollProgress = useScrollProgress();

  const baseClasses =
    variant === 'premium'
      ? 'luxury-scroll-indicator'
      : 'fixed top-0 left-0 w-full h-1 bg-gray-900 z-50';

  return (
    <div className={`${baseClasses} ${className}`}>
      <div
        className="h-full bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 transition-transform duration-150 ease-out origin-left"
        style={{ transform: `scaleX(${scrollProgress})` }}
      />
    </div>
  );
};

export default ScrollProgress;
