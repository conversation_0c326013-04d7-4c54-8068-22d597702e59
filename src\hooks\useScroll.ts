import { useEffect, useState } from 'react';
import { throttle } from '../utils/performance';

interface UseScrollOptions {
  throttleMs?: number;
  passive?: boolean;
}

export const useScrollY = (options: UseScrollOptions = {}) => {
  const { throttleMs = 16, passive = true } = options;
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = throttle(() => {
      setScrollY(window.scrollY);
    }, throttleMs);

    window.addEventListener('scroll', handleScroll, { passive });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [throttleMs, passive]);

  return scrollY;
};

export const useScrollProgress = (options: UseScrollOptions = {}) => {
  const { throttleMs = 16, passive = true } = options;
  const [scrollProgress, setScrollProgress] = useState(0);

  useEffect(() => {
    const updateScrollProgress = throttle(() => {
      const scrollPx = document.documentElement.scrollTop;
      const winHeightPx =
        document.documentElement.scrollHeight - document.documentElement.clientHeight;
      const scrolled = winHeightPx > 0 ? scrollPx / winHeightPx : 0;
      setScrollProgress(scrolled);
    }, throttleMs);

    window.addEventListener('scroll', updateScrollProgress, { passive });
    return () => window.removeEventListener('scroll', updateScrollProgress);
  }, [throttleMs, passive]);

  return scrollProgress;
};

export const useScrollDirection = (options: UseScrollOptions = {}) => {
  const { throttleMs = 16, passive = true } = options;
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null);
  const [lastScrollY, setLastScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = throttle(() => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY) {
        setScrollDirection('down');
      } else if (currentScrollY < lastScrollY) {
        setScrollDirection('up');
      }

      setLastScrollY(currentScrollY);
    }, throttleMs);

    window.addEventListener('scroll', handleScroll, { passive });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY, throttleMs, passive]);

  return scrollDirection;
};

export const useScrollThreshold = (threshold: number, options: UseScrollOptions = {}) => {
  const { throttleMs = 16, passive = true } = options;
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = throttle(() => {
      setIsScrolled(window.scrollY > threshold);
    }, throttleMs);

    window.addEventListener('scroll', handleScroll, { passive });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [threshold, throttleMs, passive]);

  return isScrolled;
};
