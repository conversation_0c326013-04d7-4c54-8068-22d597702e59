import { ArrowRight, Download, Eye, Maximize2, <PERSON><PERSON><PERSON>, Star } from 'lucide-react';
import React, { useState } from 'react';

interface Product {
  id: number;
  name: string;
  category: string;
  image: string;
  description: string;
  specifications: string[];
  applications: string[];
}

const ProductGallery: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('All');
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const categories = ['All', 'Extrusions', 'Sheets', 'Profiles', 'Custom'];

  const products = [
    {
      id: 1,
      title: 'Architectural Aluminum Extrusions',
      category: 'Extrusions',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800',
      description:
        'High-precision aluminum extrusions specifically engineered for architectural applications',
      specs: {
        alloy: '6061-T6',
        tolerance: '±0.1mm',
        finish: 'Anodized Class I',
        thickness: '1.5mm - 25mm',
        applications: ['Curtain Walls', 'Window Frames', 'Structural Glazing', 'Facade Systems'],
      },
      technicalData: {
        tensileStrength: '310 MPa',
        yieldStrength: '276 MPa',
        elongation: '12%',
        hardness: '95 HB',
      },
      certifications: ['ISO 9001:2015', 'ASTM B221', 'EN 755-2'],
      price: '$12.50 - $45.00/kg',
      availability: 'In Stock',
      leadTime: '2-3 weeks',
      rating: 5,
      featured: true,
    },
    {
      id: 2,
      title: 'Marine Grade Aluminum Sheets',
      category: 'Sheets',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800',
      description: 'Corrosion-resistant aluminum sheets engineered for harsh marine environments',
      specs: {
        alloy: '5083-H116',
        tolerance: '±0.05mm',
        finish: 'Mill Finish',
        thickness: '0.5mm - 50mm',
        applications: ['Boat Hulls', 'Marine Hardware', 'Offshore Platforms'],
      },
      technicalData: {
        tensileStrength: '317 MPa',
        yieldStrength: '228 MPa',
        elongation: '16%',
        hardness: '85 HB',
      },
      certifications: ["Lloyd's Register", 'DNV GL', 'ABS'],
      price: '$8.75 - $32.00/kg',
      availability: 'In Stock',
      leadTime: '1-2 weeks',
      rating: 5,
      featured: true,
    },
    {
      id: 3,
      title: 'High-Strength Structural Profiles',
      category: 'Profiles',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800',
      description: 'Load-bearing aluminum profiles designed for demanding structural applications',
      specs: {
        alloy: '6082-T6',
        tolerance: '±0.08mm',
        finish: 'Powder Coated',
        dimensions: 'Custom profiles available',
        applications: ['Building Frames', 'Industrial Structures', 'Transportation'],
      },
      technicalData: {
        tensileStrength: '340 MPa',
        yieldStrength: '290 MPa',
        elongation: '10%',
        hardness: '100 HB',
      },
      certifications: ['EN 755-2', 'ISO 9001:2015', 'CE Marking'],
      price: '$15.25 - $55.00/kg',
      availability: 'Made to Order',
      leadTime: '3-4 weeks',
      rating: 5,
      featured: false,
    },
    {
      id: 4,
      title: 'Aerospace Precision Components',
      category: 'Custom',
      image:
        'https://images.pexels.com/photos/1108101/pexels-photo-1108101.jpeg?auto=compress&cs=tinysrgb&w=800',
      description:
        'Ultra-precision machined aluminum components meeting stringent aerospace industry requirements',
      specs: {
        alloy: '7075-T6',
        tolerance: '±0.01mm',
        finish: 'Anodized Type III',
        machining: 'CNC 5-axis precision',
        applications: ['Aircraft Structures', 'Engine Components', 'Landing Gear'],
      },
      technicalData: {
        tensileStrength: '572 MPa',
        yieldStrength: '503 MPa',
        elongation: '11%',
        hardness: '150 HB',
      },
      certifications: ['AS9100D', 'NADCAP', 'FAA PMA'],
      price: 'Contact for Quote',
      availability: 'Made to Order',
      leadTime: '6-8 weeks',
      rating: 5,
      featured: true,
    },
  ];

  const filteredProducts =
    activeCategory === 'All'
      ? products
      : products.filter(product => product.category === activeCategory);

  const handleCategoryChange = (category: string) => {
    if (category === activeCategory) return;

    setIsTransitioning(true);
    setTimeout(() => {
      setActiveCategory(category);
      setIsTransitioning(false);
    }, 150);
  };

  const openProductModal = (product: Product) => {
    setSelectedProduct(product);
    document.body.style.overflow = 'hidden';
  };

  const closeProductModal = () => {
    setSelectedProduct(null);
    document.body.style.overflow = 'unset';
  };

  return (
    <section id="products" className="py-20 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16 animate-on-scroll">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-thin text-white mb-6">
            Product
            <span className="block metallic-gradient font-light">Portfolio</span>
          </h2>
          <p className="text-lg text-gray-400 max-w-3xl mx-auto leading-relaxed">
            Discover our comprehensive range of premium aluminum products, each engineered with
            precision and manufactured to exceed industry standards with complete technical
            documentation.
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12 animate-on-scroll">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => handleCategoryChange(category)}
              className={`px-8 py-4 rounded-full text-sm font-medium transition-all duration-300 ${
                activeCategory === category
                  ? 'metallic-button shadow-lg transform scale-105'
                  : 'glass-effect text-gray-300 hover:text-white hover:scale-105'
              }`}
              aria-pressed={activeCategory === category}>
              {category}
              <span className="ml-2 text-xs opacity-75">
                (
                {category === 'All'
                  ? products.length
                  : products.filter(p => p.category === category).length}
                )
              </span>
            </button>
          ))}
        </div>

        {/* Products Grid */}
        <div
          className={`grid md:grid-cols-2 lg:grid-cols-2 gap-8 transition-opacity duration-300 ${
            isTransitioning ? 'opacity-50' : 'opacity-100'
          }`}>
          {filteredProducts.map((product, index) => (
            <div
              key={product.id}
              className="group hover-lift animate-on-scroll"
              style={{ animationDelay: `${index * 0.1}s` }}>
              <div className="glass-effect rounded-2xl overflow-hidden h-full relative">
                {/* Featured Badge */}
                {product.featured && (
                  <div className="absolute top-4 left-4 z-10">
                    <span className="px-3 py-1 bg-gradient-to-r from-yellow-400 to-yellow-600 text-black text-xs font-bold rounded-full">
                      FEATURED
                    </span>
                  </div>
                )}

                <div className="relative overflow-hidden">
                  <img
                    src={product.image}
                    alt={`${product.title} - High-quality aluminum ${product.category.toLowerCase()} for industrial applications`}
                    className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-4 right-4 flex space-x-2">
                      <button
                        onClick={() => openProductModal(product)}
                        className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center hover:bg-white/30 transition-colors duration-200"
                        aria-label="View product details">
                        <Eye className="w-5 h-5 text-white" />
                      </button>
                      <button className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center hover:bg-white/30 transition-colors duration-200">
                        <Maximize2 className="w-5 h-5 text-white" />
                      </button>
                    </div>
                  </div>
                  <div className="absolute top-4 right-4">
                    <div className="flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
                      {[...Array(product.rating)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 text-yellow-400 fill-current" />
                      ))}
                      <span className="text-white text-xs ml-1">{product.rating}.0</span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="px-2 py-1 bg-gray-800 rounded text-xs text-gray-300 font-medium">
                      {product.category}
                    </span>
                    <span
                      className={`text-xs font-medium ${product.availability === 'In Stock' ? 'text-green-400' : 'text-yellow-400'}`}>
                      {product.availability}
                    </span>
                  </div>

                  <h3 className="text-xl font-semibold text-white mb-3 line-clamp-2">
                    {product.title}
                  </h3>
                  <p className="text-gray-400 mb-4 leading-relaxed text-sm line-clamp-3">
                    {product.description}
                  </p>

                  {/* Key Specifications */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-white mb-2">Key Specifications</h4>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Alloy:</span>
                        <span className="text-gray-300 font-medium">{product.specs.alloy}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Tolerance:</span>
                        <span className="text-gray-300 font-medium">{product.specs.tolerance}</span>
                      </div>
                    </div>
                  </div>

                  {/* Pricing and Lead Time */}
                  <div className="mb-4 p-3 bg-gray-800/50 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-400">Price Range:</span>
                      <span className="text-sm font-semibold text-white">{product.price}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-400">Lead Time:</span>
                      <span className="text-sm text-gray-300">{product.leadTime}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => openProductModal(product)}
                      className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors duration-300 group">
                      <span className="text-sm font-medium">View Details</span>
                      <ArrowRight
                        size={16}
                        className="group-hover:translate-x-1 transition-transform duration-300"
                      />
                    </button>

                    <button className="px-4 py-2 bg-gradient-to-r from-gray-400 to-yellow-500 text-black text-sm font-semibold rounded-lg hover:scale-105 transition-transform duration-200 flex items-center space-x-2">
                      <ShoppingCart size={14} />
                      <span>Quote</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Product Detail Modal */}
        {selectedProduct && (
          <div className="fixed inset-0 bg-black/90 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-gray-900 rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-8">
                <div className="flex justify-between items-start mb-6">
                  <div>
                    <h3 className="text-3xl font-semibold text-white mb-2">
                      {selectedProduct.title}
                    </h3>
                    <div className="flex items-center space-x-4">
                      <span className="px-3 py-1 bg-gray-800 rounded-full text-sm text-gray-300">
                        {selectedProduct.category}
                      </span>
                      <div className="flex items-center space-x-1">
                        {[...Array(selectedProduct.rating)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                        <span className="text-gray-400 text-sm ml-1">
                          ({selectedProduct.rating}.0)
                        </span>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={closeProductModal}
                    className="text-gray-400 hover:text-white transition-colors duration-200 text-2xl">
                    ✕
                  </button>
                </div>

                <div className="grid lg:grid-cols-2 gap-8">
                  <div>
                    <img
                      src={selectedProduct.image}
                      alt={selectedProduct.title}
                      className="w-full h-80 object-cover rounded-lg mb-6"
                    />
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-white mb-3">Description</h4>
                      <p className="text-gray-300 leading-relaxed">{selectedProduct.description}</p>
                    </div>

                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-white mb-3">Applications</h4>
                      <div className="grid grid-cols-1 gap-2">
                        {selectedProduct.specs.applications.map((app: string, index: number) => (
                          <div key={index} className="text-gray-300 text-sm flex items-center">
                            <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mr-3"></div>
                            {app}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-white mb-4">
                        Technical Specifications
                      </h4>
                      <div className="space-y-3">
                        {Object.entries(selectedProduct.technicalData).map(([key, value]) => (
                          <div
                            key={key}
                            className="flex justify-between py-2 border-b border-gray-700">
                            <span className="text-gray-400 capitalize">
                              {key.replace(/([A-Z])/g, ' $1')}:
                            </span>
                            <span className="text-gray-300 font-medium">{value}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="mb-6">
                      <h4 className="text-lg font-semibold text-white mb-4">Certifications</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedProduct.certifications.map((cert: string, index: number) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-green-900/30 border border-green-700 rounded-full text-green-300 text-sm">
                            {cert}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="mb-6 p-4 bg-gray-800 rounded-lg">
                      <h4 className="text-lg font-semibold text-white mb-3">
                        Pricing & Availability
                      </h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Price Range:</span>
                          <span className="text-white font-semibold">{selectedProduct.price}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Availability:</span>
                          <span
                            className={`font-medium ${selectedProduct.availability === 'In Stock' ? 'text-green-400' : 'text-yellow-400'}`}>
                            {selectedProduct.availability}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Lead Time:</span>
                          <span className="text-gray-300">{selectedProduct.leadTime}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex space-x-4">
                      <button className="flex-1 bg-gradient-to-r from-gray-400 to-yellow-500 text-black py-4 rounded-lg font-semibold hover:scale-105 transition-transform duration-200 flex items-center justify-center space-x-2">
                        <ShoppingCart size={18} />
                        <span>Request Quote</span>
                      </button>
                      <button className="flex items-center space-x-2 px-6 py-4 border border-gray-600 rounded-lg text-gray-300 hover:text-white transition-colors duration-200">
                        <Download size={16} />
                        <span>Datasheet</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProductGallery;
