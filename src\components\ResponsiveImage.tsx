import React, { useState, useRef, useEffect } from 'react';
import {
  optimizeImageUrl,
  generatePlaceholder,
  generateAltText,
  generateResponsiveSources,
  getOptimalImageFormat,
  measureImageLoadTime,
} from '../utils/imageUtils';

interface ResponsiveImageProps {
  src: string;
  alt?: string;
  className?: string;
  width?: number;
  height?: number;
  quality?: number;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
  context?: string;
  description?: string;
  onLoad?: () => void;
  onError?: () => void;
  showLoadingMetrics?: boolean;
}

const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  className = '',
  width = 800,
  height = 600,
  quality = 85,
  loading = 'lazy',
  priority = false,
  context = 'manufacturing',
  description,
  onLoad,
  onError,
  showLoadingMetrics = false,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(loading === 'eager' || priority);
  const [loadTime, setLoadTime] = useState<number | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Generate optimized sources
  const optimizedSrc = optimizeImageUrl(src, { width, height, quality });
  const optimalFormatSrc = getOptimalImageFormat(optimizedSrc);
  const responsiveSources = generateResponsiveSources(src, { quality });
  const placeholder = generatePlaceholder(width, height);
  const generatedAlt = alt || generateAltText(context, description);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (loading === 'eager' || priority) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [loading, priority]);

  // Preload critical images
  useEffect(() => {
    if (priority) {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = optimalFormatSrc;
      document.head.appendChild(link);

      return () => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      };
    }
  }, [priority, optimalFormatSrc]);

  const handleLoad = async () => {
    setIsLoaded(true);

    if (showLoadingMetrics) {
      const time = await measureImageLoadTime(optimalFormatSrc);
      setLoadTime(time);
    }

    onLoad?.();
  };

  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      style={{ aspectRatio: `${width}/${height}` }}>
      {/* Placeholder */}
      {!isLoaded && !hasError && (
        <img
          src={placeholder}
          alt=""
          className="absolute inset-0 w-full h-full object-cover blur-sm opacity-50"
          aria-hidden="true"
        />
      )}

      {/* Main Image with Picture element for responsive sources */}
      {isInView && !hasError && (
        <picture>
          {/* Modern format sources */}
          {responsiveSources.map((source, index) => (
            <source key={index} srcSet={source.srcSet} media={source.media} type={source.type} />
          ))}

          {/* Fallback image */}
          <img
            ref={imgRef}
            src={optimizedSrc}
            alt={generatedAlt}
            className={`w-full h-full object-cover transition-opacity duration-500 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            }`}
            onLoad={handleLoad}
            onError={handleError}
            loading={loading}
            decoding="async"
            width={width}
            height={height}
          />
        </picture>
      )}

      {/* Error State */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-800 text-gray-400">
          <div className="text-center p-4">
            <div className="text-2xl mb-2">⚠️</div>
            <div className="text-sm">Failed to load image</div>
            <div className="text-xs text-gray-500 mt-1">{src.split('/').pop()}</div>
          </div>
        </div>
      )}

      {/* Loading Metrics (Development) */}
      {showLoadingMetrics && loadTime !== null && (
        <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
          {loadTime > 0 ? `${loadTime.toFixed(0)}ms` : 'Error'}
        </div>
      )}

      {/* Loading Indicator */}
      {!isLoaded && !hasError && isInView && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-gray-400 border-t-yellow-400 rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
};

export default ResponsiveImage;
