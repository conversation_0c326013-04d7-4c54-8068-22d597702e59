import { useEffect, useState } from 'react';
import AnimationShowcase from './components/AnimationShowcase';
import ContactSection from './components/ContactSection';
import ErrorBoundary from './components/ErrorBoundary';
import Footer from './components/Footer';
import HeroSection from './components/HeroSection';
import InnovationSection from './components/InnovationSection';
import LoadingScreen from './components/LoadingScreen';
import ManufacturingSection from './components/ManufacturingSection';
import PortfolioSection from './components/PortfolioSection';
import PremiumProductShowcase from './components/PremiumProductShowcase';
import ProductGallery from './components/ProductGallery';
import ScrollProgress from './components/ScrollProgress';
import { initializeImagePreloading } from './utils/imagePreloader';
import { performanceMonitor } from './utils/performance';

function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Start performance monitoring
    performanceMonitor.startTiming('app-initialization');

    // Initialize image preloading
    initializeImagePreloading();

    // Enhanced Intersection Observer for animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px',
    };

    const observer = new IntersectionObserver(entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');

          // Add staggered animation for child elements
          const children = entry.target.querySelectorAll('.stagger-child');
          children.forEach((child, index) => {
            setTimeout(() => {
              child.classList.add('animate-fade-in');
            }, index * 100);
          });
        }
      });
    }, observerOptions);

    // Observe all elements with animate-on-scroll class
    const animateElements = document.querySelectorAll('.animate-on-scroll');
    animateElements.forEach(el => observer.observe(el));

    // Scroll-based effects
    const handleScroll = () => {
      const scrolled = window.scrollY;

      // Parallax effects
      const parallaxElements = document.querySelectorAll('.parallax-element');
      parallaxElements.forEach(element => {
        const speed = element.getAttribute('data-speed') || '0.5';
        const yPos = -(scrolled * parseFloat(speed));
        (element as HTMLElement).style.transform = `translateY(${yPos}px)`;
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });

    // End performance monitoring
    performanceMonitor.endTiming('app-initialization');

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isLoading]);

  const handleLoadingComplete = () => {
    performanceMonitor.startTiming('app-render');
    setIsLoading(false);
    // End timing will be handled when components are fully rendered
    setTimeout(() => {
      performanceMonitor.endTiming('app-render');
    }, 100);
  };

  if (isLoading) {
    return <LoadingScreen onComplete={handleLoadingComplete} />;
  }

  return (
    <ErrorBoundary level="page" showDetails={true}>
      <div className="font-sans bg-black text-white">
        {/* Scroll Progress */}
        <ScrollProgress />

        {/* Skip Link for Accessibility */}
        <a
          href="#main-content"
          className="skip-link sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-white focus:text-black focus:rounded">
          Skip to main content
        </a>

        <ErrorBoundary level="section">
          <main id="main-content">
            <ErrorBoundary level="component">
              <HeroSection />
            </ErrorBoundary>
            <ErrorBoundary level="component">
              <ManufacturingSection />
            </ErrorBoundary>
            <ErrorBoundary level="component">
              <ProductGallery />
            </ErrorBoundary>
            <ErrorBoundary level="component">
              <PremiumProductShowcase />
            </ErrorBoundary>
            <ErrorBoundary level="component">
              <PortfolioSection />
            </ErrorBoundary>
            <ErrorBoundary level="component">
              <AnimationShowcase />
            </ErrorBoundary>
            <ErrorBoundary level="component">
              <InnovationSection />
            </ErrorBoundary>
            <ErrorBoundary level="component">
              <ContactSection />
            </ErrorBoundary>
          </main>
        </ErrorBoundary>
        <ErrorBoundary level="component">
          <Footer />
        </ErrorBoundary>
      </div>
    </ErrorBoundary>
  );
}

export default App;
